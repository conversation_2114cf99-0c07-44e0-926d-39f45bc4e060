# ThinkFlash 🧠

A powerful thinking training application that helps you develop critical thinking skills through AI-powered question generation and interactive analysis tools.

## ✨ Features

### 🎯 Question Generation
- **AI-Powered Questions**: Generate thoughtful questions using Google Gemini API
- **Multiple Categories**: Critical thinking, creative thinking, logical reasoning, systems thinking, ethical analysis, and practical application
- **Difficulty Levels**: Beginner, intermediate, and advanced questions
- **Local Fallback**: Works offline with built-in question templates
- **Multi-language Support**: English and Chinese interface

### 🔍 Analysis Tools
- **Systemic Analysis**: Analyze topics using various thinking models:
  - 🦋 Butterfly Effect Analysis
  - 🔍 Premortem Analysis
  - ⚔️ Red Team Simulation
  - 🔗 Systems Thinking
  - 🎯 Scenario Planning
  - 🌱 Root Cause Analysis
- **Deductive Analysis**: Interactive network graphs for exploring logical connections
- **Visual Networks**: D3.js-powered interactive visualizations

### 🎨 User Experience
- **Dark/Light Mode**: Seamless theme switching
- **Responsive Design**: Works on desktop and mobile devices
- **History Management**: Save and revisit previous sessions
- **Export Functionality**: Export results in Markdown, PDF, or Word formats
- **Glass Morphism UI**: Modern, elegant interface design

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd thinkflash
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

## 🔧 Configuration

### AI Model Setup
1. Click the settings icon in the bottom navigation
2. Choose your preferred AI provider (Gemini, DeepSeek, Zhipu, or Custom)
3. Enter your API key
4. Configure model parameters as needed

**Supported Providers:**
- **Google Gemini**: Requires Gemini API key
- **DeepSeek**: Custom API endpoint support
- **Zhipu AI**: Chinese AI provider
- **Custom**: Configure your own API endpoint

### Environment Variables
Create a `.env` file in the root directory:

```env
# Optional: Set default API keys
VITE_GEMINI_API_KEY=your_gemini_api_key_here
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

## 📖 Usage Guide

### Basic Question Generation
1. Enter any topic, concept, or entity in the search box
2. Press Enter or click the generate button
3. Browse generated questions by category
4. Use the hints and estimated time to guide your thinking

### Systemic Analysis
1. Navigate to the Analysis page
2. Enter your topic
3. Select thinking models to apply
4. Adjust analysis depth (1-3 levels)
5. Start analysis and explore the interactive network

### Deductive Analysis
1. Go to the Deductive Analysis page
2. Enter your initial premise or topic
3. Interact with nodes to generate positive/negative deductions
4. Use the timeline to navigate through your reasoning process
5. Toggle the node list panel for detailed information

### History and Export
- Access your history through the history button
- Export individual sessions in multiple formats
- Search through previous topics and questions

## 🛠️ Development

### Tech Stack
- **Frontend**: Vue 3 + TypeScript + Vite
- **Styling**: Tailwind CSS with custom glass morphism effects
- **Visualizations**: D3.js for interactive network graphs
- **AI Integration**: Google Generative AI SDK
- **Build Tool**: Vite with TypeScript support

### Project Structure
```
src/
├── components/          # Vue components
│   ├── ThinkingTrainer.vue    # Main question generation
│   ├── AnalysisPage.vue       # Systemic analysis
│   ├── DeductiveAnalysisPage.vue  # Deductive reasoning
│   ├── NetworkGraph.vue       # D3.js network visualization
│   └── ...
├── utils/              # Utility functions
│   ├── questionGenerator.ts   # Question templates
│   ├── systemicThinking.ts    # Analysis logic
│   ├── deductiveThinking.ts   # Deductive reasoning
│   └── geminiApi.ts          # AI integration
├── stores/             # State management
│   ├── theme.ts        # Theme switching
│   └── language.ts     # Internationalization
└── router.ts           # Vue Router configuration
```

### Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Type checking
npm run type-check
```

### Adding New Features

#### Adding a New Thinking Model
1. Add model definition to `src/utils/systemicThinking.ts`
2. Implement analysis logic in the `LLMSystemicAnalyzer` class
3. Add UI components in `AnalysisPage.vue`

#### Adding New Question Categories
1. Update `QuestionCategory` type in `src/utils/questionGenerator.ts`
2. Add question templates to the `questionTemplates` object
3. Update category labels in components

#### Customizing the UI
- Modify Tailwind classes in components
- Update theme colors in `src/stores/theme.ts`
- Customize glass morphism effects in `src/style.css`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style
- Use TypeScript for type safety
- Follow Vue 3 Composition API patterns
- Use Tailwind CSS for styling
- Add JSDoc comments for complex functions

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Vue.js Team** for the excellent framework
- **Google** for the Generative AI API
- **D3.js Community** for powerful visualization tools
- **Tailwind CSS** for the utility-first CSS framework

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) page
2. Create a new issue with detailed information
3. Include steps to reproduce any bugs

## 🔮 Roadmap

- [ ] **Advanced Analytics**: Add more sophisticated analysis models
- [ ] **Collaboration Features**: Share and collaborate on thinking sessions
- [ ] **Mobile App**: Native mobile applications
- [ ] **Plugin System**: Extensible architecture for custom thinking models
- [ ] **Learning Paths**: Guided thinking skill development
- [ ] **Community Hub**: Share and discover thinking frameworks

---

**Made with ❤️ for better thinking**
