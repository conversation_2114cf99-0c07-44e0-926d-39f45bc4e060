<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import DeductiveTreeGraph from './DeductiveTreeGraph.vue'
import { LLMDeductiveAnalyzer } from '../utils/deductiveThinking'
import { AnalysisNode, NodeConnection } from '../utils/systemicThinking'

const route = useRoute()
const topic = ref('')
const nodes = ref<AnalysisNode[]>([])
const edges = ref<NodeConnection[]>([])
const isLoading = ref(true)
const error = ref<string | null>(null)

const generateDeductiveAnalysis = async (initialTopic: string, thinkingPrompt: string) => {
  isLoading.value = true
  error.value = null
  nodes.value = []
  edges.value = []

  try {
    const analyzer = new LLMDeductiveAnalyzer()
    const result = await analyzer.generateDeductiveAnalysis(initialTopic, thinkingPrompt)

    // Add initial node
    const initialNode: AnalysisNode = {
      id: 'initial-0',
      title: result.initialTopic,
      description: '初始思考提示',
      level: 0,
      impact: 'medium',
      modelSource: 'initial',
      type: 'initial'
    }
    nodes.value.push(initialNode)

    // Add positive branch nodes and connections
    nodes.value.push(...result.positiveBranch.nodes)
    edges.value.push(...result.positiveBranch.connections)
    if (result.positiveBranch.nodes.length > 0) {
      edges.value.push({
        from: initialNode.id,
        to: result.positiveBranch.nodes[0].id,
        type: 'strong',
        description: '积极方向',
        strength: 1
      })
    }

    // Add negative branch nodes and connections
    nodes.value.push(...result.negativeBranch.nodes)
    edges.value.push(...result.negativeBranch.connections)
    if (result.negativeBranch.nodes.length > 0) {
      edges.value.push({
        from: initialNode.id,
        to: result.negativeBranch.nodes[0].id,
        type: 'strong',
        description: '负面方向',
        strength: 1
      })
    }

  } catch (e: any) {
    console.error('Failed to generate deductive analysis:', e)
    error.value = e.message || '生成推演分析失败'
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  if (route.query.topic) {
    topic.value = route.query.topic as string
    const thinkingPrompt = (route.query.thinkingPrompt as string) || '';
    console.log('DeductiveAnalysisPage: Initializing with topic', topic.value, 'and thinkingPrompt', thinkingPrompt);
    generateDeductiveAnalysis(topic.value, thinkingPrompt)
  } else {
    // 如果没有topic参数，显示错误信息
    error.value = '缺少分析主题参数'
    isLoading.value = false
  }
})

watch([nodes, edges], () => {
  console.log('DeductiveAnalysisPage: Nodes updated', nodes.value);
  console.log('DeductiveAnalysisPage: Edges updated', edges.value);
});
</script>

<template>
  <div class="deductive-analysis-page p-6 bg-white dark:bg-slate-900 min-h-screen">
    <h1 class="text-2xl font-bold mb-4 text-slate-800 dark:text-slate-200">推演分析: {{ topic }}</h1>

    <div v-if="isLoading" class="flex items-center justify-center h-[600px] text-slate-500 dark:text-slate-400">
      <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span>正在生成推演分析...</span>
    </div>

    <div v-else-if="error" class="flex items-center justify-center h-[600px] text-red-500 dark:text-red-400">
      <p>错误: {{ error }}</p>
    </div>

    <div v-else-if="nodes.length === 0" class="flex items-center justify-center h-[600px] text-slate-500 dark:text-slate-400">
      <p>没有生成任何推演节点。请尝试更换主题或提示。</p>
    </div>
    <div v-else class="graph-container h-[600px] border border-slate-200 dark:border-slate-700 rounded-lg shadow-md">
      <DeductiveTreeGraph :nodes="nodes" :connections="edges" />
    </div>
  </div>
</template>

<style scoped>
/* Add any specific styles for this page here */
</style>
