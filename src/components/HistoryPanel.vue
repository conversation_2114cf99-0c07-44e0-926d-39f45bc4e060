<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { language } from '../stores/language'
import type { Question, QuestionCategory } from '../utils/questionGenerator'

interface HistoryRecord {
  id: string
  topic: string
  questions: Question[]
  timestamp: number
}

interface Props {
  show: boolean
  history: HistoryRecord[]
}

interface Emits {
  (e: 'close'): void
  (e: 'load-history', record: HistoryRecord): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const searchQuery = ref('')
const selectedRecord = ref<HistoryRecord | null>(null)
const isExporting = ref(false)
const exportFormat = ref<'md' | 'pdf' | 'doc'>('md')

watch(language, () => {
  searchQuery.value = ''
})

const placeholderText = computed(() => {
  return language.value === 'zh' ? '搜索历史记录...' : 'Search history...'
})

const filteredHistory = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.history
  }
  
  const query = searchQuery.value.toLowerCase()
  return props.history.filter(record => 
    record.topic.toLowerCase().includes(query) ||
    record.questions.some(q => 
      q.question.toLowerCase().includes(query) ||
      q.description.toLowerCase().includes(query)
    )
  )
})

const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleString(language.value === 'zh' ? 'zh-CN' : 'en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const categoryLabels = computed<Record<QuestionCategory, string>>(() => {
  if (language.value === 'zh') {
    return {
      critical: '批判性思维',
      creative: '创意思维',
      logical: '逻辑分析',
      system: '系统思维',
      ethical: '伦理思考',
      practical: '实践应用'
    }
  } else {
    return {
      critical: 'Critical Thinking',
      creative: 'Creative Thinking',
      logical: 'Logical Analysis',
      system: 'System Thinking',
      ethical: 'Ethical Consideration',
      practical: 'Practical Application'
    }
  }
})

const difficultyLabels = computed(() => {
  if (language.value === 'zh') {
    return {
      beginner: '入门',
      intermediate: '中级',
      advanced: '高级'
    }
  } else {
    return {
      beginner: 'Beginner',
      intermediate: 'Intermediate',
      advanced: 'Advanced'
    }
  }
})

const loadRecord = (record: HistoryRecord) => {
  emit('load-history', record)
}

const viewRecord = (record: HistoryRecord) => {
  selectedRecord.value = record
}

const closeDetail = () => {
  selectedRecord.value = null
}

// 导出为Markdown格式
const exportToMarkdown = (record: HistoryRecord) => {
  let markdown = language.value === 'zh' ? `# 思维训练问题集\n\n` : `# Thinking Training Question Set\n\n`
  markdown += language.value === 'zh' ? `**主题**: ${record.topic}\n\n` : `**Topic**: ${record.topic}\n\n`
  markdown += language.value === 'zh' ? `**生成时间**: ${formatDate(record.timestamp)}\n\n` : `**Generated Time**: ${formatDate(record.timestamp)}\n\n`
  markdown += language.value === 'zh' ? `**问题数量**: ${record.questions.length}\n\n` : `**Number of Questions**: ${record.questions.length}\n\n`
  markdown += `---\n\n`
  
  record.questions.forEach((question, index) => {
    markdown += `## ${index + 1}. ${question.question}\n\n`
    markdown += language.value === 'zh' ? `**类别**: ${categoryLabels.value[question.category] || '未知类别'}\n\n` : `**Category**: ${categoryLabels.value[question.category] || 'Unknown Category'}\n\n`
    markdown += language.value === 'zh' ? `**难度**: ${difficultyLabels.value[question.difficulty] || '未知难度'}\n\n` : `**Difficulty**: ${difficultyLabels.value[question.difficulty] || 'Unknown Difficulty'}\n\n`
    markdown += language.value === 'zh' ? `**预估时间**: ${question.estimatedTime}\n\n` : `**Estimated Time**: ${question.estimatedTime}\n\n`
    
    if (question.description) {
      markdown += language.value === 'zh' ? `**描述**: ${question.description}\n\n` : `**Description**: ${question.description}\n\n`
    }
    
    if (question.hints.length > 0) {
      markdown += language.value === 'zh' ? `**思考提示**:\n\n` : `**Thinking Hints**:\n\n`
      question.hints.forEach(hint => {
        markdown += `- ${hint}\n`
      })
      markdown += `\n`
    }
    
    markdown += `---\n\n`
  })
  
  return markdown
}

// 导出为PDF格式（简化版）
const exportToPDF = (record: HistoryRecord) => {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>${language.value === 'zh' ? '思维训练问题集' : 'Thinking Training Question Set'} - ${record.topic}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1 { color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px; }
        h2 { color: #1e40af; margin-top: 30px; }
        .meta { background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .question { margin: 20px 0; padding: 15px; border-left: 4px solid #3b82f6; }
        .hints { background: #eff6ff; padding: 10px; border-radius: 5px; }
        .badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-right: 5px; }
        .category { background: #dbeafe; color: #1e40af; }
        .difficulty { background: #fef3c7; color: #92400e; }
      </style>
    </head>
    <body>
      <h1>${language.value === 'zh' ? '思维训练问题集' : 'Thinking Training Question Set'}</h1>
      <div class="meta">
        <p><strong>${language.value === 'zh' ? '主题' : 'Topic'}:</strong> ${record.topic}</p>
        <p><strong>${language.value === 'zh' ? '生成时间' : 'Generated Time'}:</strong> ${formatDate(record.timestamp)}</p>
        <p><strong>${language.value === 'zh' ? '问题数量' : 'Number of Questions'}:</strong> ${record.questions.length}</p>
      </div>
      ${record.questions.map((question, index) => `
        <div class="question">
          <h2>${index + 1}. ${question.question}</h2>
          <p>
            <span class="badge category">${categoryLabels.value[question.category] || (language.value === 'zh' ? '未知类别' : 'Unknown Category')}</span>
            <span class="badge difficulty">${difficultyLabels.value[question.difficulty] || (language.value === 'zh' ? '未知难度' : 'Unknown Difficulty')}</span>
            <span class="badge">${question.estimatedTime}</span>
          </p>
          ${question.description ? `<p><strong>描述:</strong> ${question.description}</p>` : ''}
          ${question.hints.length > 0 ? `
            <div class="hints">
              <strong>思考提示:</strong>
              <ul>
                ${question.hints.map(hint => `<li>${hint}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
      `).join('')}
    </body>
    </html>
  `
  
  // 打开新窗口并打印
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(htmlContent)
    printWindow.document.close()
    printWindow.focus()
    setTimeout(() => {
      printWindow.print()
    }, 500)
  }
}

// 导出为DOC格式（简化版）
const exportToDoc = (record: HistoryRecord) => {
  let htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>${language.value === 'zh' ? '思维训练问题集' : 'Thinking Training Question Set'} - ${record.topic}</title>
    </head>
    <body>
      <h1>${language.value === 'zh' ? '思维训练问题集' : 'Thinking Training Question Set'}</h1>
      <p><strong>${language.value === 'zh' ? '主题' : 'Topic'}:</strong> ${record.topic}</p>
      <p><strong>${language.value === 'zh' ? '生成时间' : 'Generated Time'}:</strong> ${formatDate(record.timestamp)}</p>
      <p><strong>${language.value === 'zh' ? '问题数量' : 'Number of Questions'}:</strong> ${record.questions.length}</p>
      <hr/>
      <p>Question details will be exported here.</p>
    </body>
    </html>
  `
  return htmlContent
}

const exportRecord = async (record: HistoryRecord) => {
  isExporting.value = true
  
  try {
    let content: string
    let filename: string
    let mimeType: string
    
    const dateStr = new Date().toISOString().split('T')[0]
    
    switch (exportFormat.value) {
      case 'md':
        content = exportToMarkdown(record)
        filename = `思维训练-${record.topic}-${dateStr}.md`
        mimeType = 'text/markdown;charset=utf-8'
        break
      case 'pdf':
        exportToPDF(record)
        return
      case 'doc':
        content = exportToDoc(record)
        filename = `思维训练-${record.topic}-${dateStr}.doc`
        mimeType = 'application/msword'
        break
      default:
        content = exportToMarkdown(record)
        filename = `思维训练-${record.topic}-${dateStr}.md`
        mimeType = 'text/markdown;charset=utf-8'
    }
    
    // 创建并下载文件
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('导出失败:', error)
    alert(language.value === 'zh' ? '导出失败，请重试' : 'Export failed, please try again')
  } finally {
    isExporting.value = false
  }
}

const deleteRecord = (recordId: string) => {
  if (confirm(language.value === 'zh' ? '确定要删除这条历史记录吗？' : 'Are you sure you want to delete this history record?')) {
    const updatedHistory = props.history.filter(r => r.id !== recordId)
    localStorage.setItem('thinking-history', JSON.stringify(updatedHistory))
    window.dispatchEvent(new CustomEvent('history-updated'))
  }
}

const clearAllHistory = () => {
  if (confirm(language.value === 'zh' ? '确定要清空所有历史记录吗？此操作不可恢复。' : 'Are you sure you want to clear all history records? This action cannot be undone.')) {
    localStorage.removeItem('thinking-history')
    window.dispatchEvent(new CustomEvent('history-updated'))
    emit('close')
  }
}
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2 sm:p-4">
    <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl w-full max-w-6xl h-[95vh] sm:h-[90vh] flex flex-col overflow-hidden">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700 bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-800 dark:to-slate-700">
        <div class="flex-1 min-w-0">
          <h2 class="text-lg sm:text-2xl font-bold text-slate-800 dark:text-slate-200 truncate">{{ language === 'zh' ? '历史记录' : 'History' }}</h2>
          <p class="text-sm sm:text-base text-slate-600 dark:text-slate-400 mt-1">{{ language === 'zh' ? '共' : 'Total' }} {{ history.length }} {{ language === 'zh' ? '条记录' : 'records' }}</p>
        </div>
        <div class="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
          <button
            @click="clearAllHistory"
            class="text-xs sm:text-sm text-red-600 hover:text-red-700 px-2 sm:px-3 py-1 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
          >
            {{ language === 'zh' ? '清空全部' : 'Clear All' }}
          </button>
          <button
            @click="$emit('close')"
            class="p-1.5 sm:p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 transition-colors"
          >
            <svg class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- 搜索栏 -->
      <div class="p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700">
        <div class="relative">
          <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="placeholderText"
            class="w-full pl-8 sm:pl-10 pr-4 py-2 sm:py-3 border border-slate-200 dark:border-slate-600 rounded-lg focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 outline-none bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 text-sm sm:text-base"
          />
        </div>
      </div>

      <!-- 历史记录列表 -->
      <div class="flex-1 overflow-hidden">
        <div v-if="filteredHistory.length > 0" class="h-full overflow-y-auto p-4 sm:p-6">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            <div
              v-for="record in filteredHistory"
              :key="record.id"
              class="bg-slate-50 dark:bg-slate-700 rounded-lg p-3 sm:p-4 hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors cursor-pointer border border-slate-200 dark:border-slate-600"
            >
              <div class="flex items-start justify-between mb-2 sm:mb-3">
                <h3 class="font-semibold text-sm sm:text-base text-slate-800 dark:text-slate-200 truncate flex-1 mr-2">{{ record.topic }}</h3>
                <div class="flex items-center space-x-1 flex-shrink-0">
                  <!-- 导出格式选择 -->
                  <select
                    v-model="exportFormat"
                    @click.stop
                    class="text-xs border border-slate-300 dark:border-slate-500 rounded px-1 py-0.5 bg-white dark:bg-slate-600 text-slate-700 dark:text-slate-300"
                  >
                    <option value="md">MD</option>
                    <option value="pdf">PDF</option>
                    <option value="doc">DOC</option>
                  </select>
                  <button
                    @click.stop="exportRecord(record)"
                    :disabled="isExporting"
                    class="p-1 text-slate-400 hover:text-blue-600 transition-colors"
                    :title="language === 'zh' ? `导出${exportFormat.toUpperCase()}` : `Export ${exportFormat.toUpperCase()}`"
                  >
                    <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </button>
                  <button
                    @click.stop="deleteRecord(record.id)"
                    class="p-1 text-slate-400 hover:text-red-600 transition-colors"
                    :title="language === 'zh' ? '删除' : 'Delete'"
                  >
                    <svg class="h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
              
              <div class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-2 sm:mb-3">
                <div class="flex items-center justify-between">
                  <span>{{ record.questions.length }} {{ language === 'zh' ? '个问题' : 'questions' }}</span>
                  <span class="hidden sm:inline">{{ formatDate(record.timestamp) }}</span>
                </div>
                <div class="sm:hidden text-xs text-slate-500 dark:text-slate-400 mt-1">
                  {{ formatDate(record.timestamp) }}
                </div>
              </div>
              
              <div class="flex items-center justify-between space-x-2">
                <button
                  @click="viewRecord(record)"
                  class="text-xs sm:text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  {{ language === 'zh' ? '查看详情' : 'View Details' }}
                </button>
                <button
                  @click="loadRecord(record)"
                  class="text-xs sm:text-sm bg-primary-600 hover:bg-primary-700 text-white px-2 sm:px-3 py-1 rounded-lg transition-colors"
                >
                  {{ language === 'zh' ? '加载使用' : 'Load & Use' }}
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-else class="h-full flex items-center justify-center p-4">
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 sm:h-16 sm:w-16 text-slate-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="text-base sm:text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
              {{ searchQuery ? (language === 'zh' ? '没有找到匹配的记录' : 'No matching records found') : (language === 'zh' ? '暂无历史记录' : 'No history yet') }}
            </h3>
            <p class="text-sm sm:text-base text-slate-600 dark:text-slate-400">
              {{ searchQuery ? (language === 'zh' ? '尝试使用其他关键词搜索' : 'Try searching with different keywords') : (language === 'zh' ? '开始生成问题后，历史记录会显示在这里' : 'History will appear here after you generate questions') }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <div v-if="selectedRecord" class="fixed inset-0 bg-black bg-opacity-50 z-60 flex items-center justify-center p-2 sm:p-4">
      <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl w-full max-w-4xl h-[90vh] sm:h-[80vh] flex flex-col overflow-hidden">
        <!-- 详情头部 -->
        <div class="flex items-center justify-between p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700">
          <div class="flex-1 min-w-0">
            <h3 class="text-lg sm:text-xl font-bold text-slate-800 dark:text-slate-200 truncate">{{ selectedRecord.topic }}</h3>
            <p class="text-sm sm:text-base text-slate-600 dark:text-slate-400 mt-1">{{ formatDate(selectedRecord.timestamp) }} • {{ selectedRecord.questions.length }} {{ language === 'zh' ? '个问题' : 'questions' }}</p>
          </div>
          <div class="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
            <select
              v-model="exportFormat"
              class="text-xs sm:text-sm border border-slate-300 dark:border-slate-500 rounded px-1 sm:px-2 py-1 bg-white dark:bg-slate-600 text-slate-700 dark:text-slate-300"
            >
              <option value="md">Markdown</option>
              <option value="pdf">PDF</option>
              <option value="doc">Word</option>
            </select>
            <button
              @click="exportRecord(selectedRecord)"
              :disabled="isExporting"
              class="btn-secondary text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-4 disabled:opacity-50"
            >
              <span v-if="isExporting">{{ language === 'zh' ? '导出中...' : 'Exporting...' }}</span>
              <span v-else>{{ language === 'zh' ? '导出' : 'Export' }}{{ exportFormat.toUpperCase() }}</span>
            </button>
            <button
              @click="closeDetail"
              class="p-1.5 sm:p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 transition-colors"
            >
              <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        
        <!-- 问题列表 -->
        <div class="flex-1 overflow-y-auto p-4 sm:p-6">
          <div class="space-y-4 sm:space-y-6">
            <div
              v-for="(question, index) in selectedRecord.questions"
              :key="question.id"
              class="bg-slate-50 dark:bg-slate-700 rounded-lg p-3 sm:p-4 border border-slate-200 dark:border-slate-600"
            >
              <div class="flex items-start justify-between mb-2 sm:mb-3">
                <div class="flex items-center space-x-2">
                  <span class="bg-primary-100 text-primary-700 text-xs sm:text-sm font-medium px-2 py-1 rounded">
                    {{ index + 1 }}
                  </span>
                  <div class="flex flex-wrap gap-1 text-xs">
                    <span class="bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded">
                      {{ categoryLabels[question.category] || (language === 'zh' ? '未知类别' : 'Unknown Category') }}
                    </span>
                    <span class="bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 px-2 py-0.5 rounded">
                      {{ difficultyLabels[question.difficulty] || (language === 'zh' ? '未知难度' : 'Unknown Difficulty') }}
                    </span>
                  </div>
                </div>
                <span class="text-xs text-slate-500 dark:text-slate-400 flex-shrink-0">{{ question.estimatedTime }}</span>
              </div>
              
              <h4 class="font-semibold text-sm sm:text-base text-slate-800 dark:text-slate-200 mb-2">{{ question.question }}</h4>
              <p class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mb-3">{{ question.description }}</p>
              
              <div v-if="question.hints.length > 0">
                <h5 class="text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">思考提示：</h5>
                <ul class="space-y-1">
                  <li
                    v-for="hint in question.hints"
                    :key="hint"
                    class="text-xs sm:text-sm text-slate-600 dark:text-slate-400 flex items-start"
                  >
                    <span class="text-primary-500 mr-2 mt-0.5">•</span>
                    {{ hint }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>