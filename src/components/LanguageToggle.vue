<script setup lang="ts">
import { language, toggleLanguage } from '../stores/language'
</script>

<template>
  <button
    @click="toggleLanguage"
    class="p-1.5 sm:p-2 rounded-full glass-effect hover:bg-slate-100/80 dark:hover:bg-slate-700/80 text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 flex items-center space-x-1.5 sm:space-x-2"
    title="Toggle Language"
  >
    <svg class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 018.648 16H14m-5-6h.01M17 13l2-2m0 0l2 2m-2-2v6.5M10 16H6a2 2 0 01-2-2V6a2 2 0 012-2h5l2 2v5m-6 2h6" />
    </svg>
    <span class="hidden sm:inline text-xs sm:text-sm">{{ language === 'zh' ? 'English' : '中文' }}</span>
  </button>
</template>

<style scoped>
.glass-effect {
  background-color: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background-color: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.2);
}
</style>