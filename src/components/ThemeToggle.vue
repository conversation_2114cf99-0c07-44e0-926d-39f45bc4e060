<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { language } from '../stores/language'

type Theme = 'light' | 'dark' | 'auto'

const currentTheme = ref<Theme>('auto')
const isDark = ref(false)

// 检测系统主题
const getSystemTheme = () => {
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

// 应用主题
const applyTheme = (theme: 'light' | 'dark') => {
  isDark.value = theme === 'dark'
  document.documentElement.classList.toggle('dark', theme === 'dark')
  document.documentElement.setAttribute('data-theme', theme)
}

// 更新主题
const updateTheme = () => {
  if (currentTheme.value === 'auto') {
    const systemTheme = getSystemTheme()
    applyTheme(systemTheme)
  } else {
    applyTheme(currentTheme.value)
  }
}

// 切换主题
const toggleTheme = () => {
  const themes: Theme[] = ['light', 'dark', 'auto']
  const currentIndex = themes.indexOf(currentTheme.value)
  const nextIndex = (currentIndex + 1) % themes.length
  currentTheme.value = themes[nextIndex]
  localStorage.setItem('theme', currentTheme.value)
  updateTheme()
}

// 监听系统主题变化
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
const handleSystemThemeChange = () => {
  if (currentTheme.value === 'auto') {
    updateTheme()
  }
}

onMounted(() => {
  // 加载保存的主题
  const savedTheme = localStorage.getItem('theme') as Theme
  if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
    currentTheme.value = savedTheme
  }
  
  // 应用初始主题
  updateTheme()
  
  // 监听系统主题变化
  mediaQuery.addEventListener('change', handleSystemThemeChange)
})

watch(currentTheme, updateTheme)

const getThemeIcon = () => {
  switch (currentTheme.value) {
    case 'light':
      return '☀️'
    case 'dark':
      return '🌙'
    case 'auto':
      return '🌓'
    default:
      return '🌓'
  }
}

const getThemeLabel = computed(() => {
  if (language.value === 'zh') {
    switch (currentTheme.value) {
      case 'light':
        return '浅色模式'
      case 'dark':
        return '深色模式'
      case 'auto':
        return '自动模式'
      default:
        return '自动模式'
    }
  } else {
    switch (currentTheme.value) {
      case 'light':
        return 'Light Mode'
      case 'dark':
        return 'Dark Mode'
      case 'auto':
        return 'Auto Mode'
      default:
        return 'Auto Mode'
    }
  }
})
</script>

<template>
  <button
    @click="toggleTheme"
    class="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-4 py-1.5 sm:py-2 rounded-lg hover:bg-slate-100/80 dark:hover:bg-slate-800/80 transition-all duration-200 text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100 transform hover:scale-105"
    :title="getThemeLabel"
  >
    <span class="text-base sm:text-lg">{{ getThemeIcon() }}</span>
    <span class="text-xs sm:text-sm font-medium hidden sm:inline">{{ getThemeLabel }}</span>
  </button>
</template>