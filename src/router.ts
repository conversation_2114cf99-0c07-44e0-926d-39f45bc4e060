import { createRouter, createWebHistory } from 'vue-router'
import ThinkingTrainer from './components/ThinkingTrainer.vue'
import AnalysisPage from './components/AnalysisPage.vue'
import DeductiveAnalysisPage from './components/DeductiveAnalysisPage.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: ThinkingTrainer
  },
  {
    path: '/analysis',
    name: 'Analysis',
    component: AnalysisPage
  },
  {
    path: '/deductive-analysis',
    name: 'DeductiveAnalysis',
    component: DeductiveAnalysisPage
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router