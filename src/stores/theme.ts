import { ref, watch } from 'vue';

const THEME_STORAGE_KEY = 'app-theme';

// Reactive state for the current theme
const isDark = ref(localStorage.getItem(THEME_STORAGE_KEY) === 'dark');

// Function to apply the theme to the document element
const applyTheme = (dark: boolean) => {
  if (dark) {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
};

// Watch for changes in isDark and apply/persist the theme
watch(isDark, (newVal) => {
  applyTheme(newVal);
  localStorage.setItem(THEME_STORAGE_KEY, newVal ? 'dark' : 'light');
}, { immediate: true }); // Apply theme immediately on startup

// Function to toggle the theme
export const toggleTheme = () => {
  isDark.value = !isDark.value;
};

// Expose the theme state
export const theme = {
  isDark
};