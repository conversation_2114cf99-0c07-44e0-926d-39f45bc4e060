import { GoogleGenerativeAI } from '@google/generative-ai'
import type { Question, QuestionCategory, QuestionDifficulty } from './questionGenerator'

interface ModelConfig {
  id: string
  name: string
  provider: 'gemini' | 'deepseek' | 'zhipu' | 'custom'
  apiKey: string
  baseUrl?: string
  isActive: boolean
  isCustom?: boolean
}

export class GeminiQuestionGenerator {
  private genAI: GoogleGenerativeAI | null = null
  private model: any = null
  private config: ModelConfig | null = null

  constructor(apiKey: string, config?: ModelConfig) {
    this.config = config || null
    
    if (apiKey) {
      if (config?.provider === 'gemini' || !config) {
        // 使用 Gemini API
        this.genAI = new GoogleGenerativeAI(apiKey)
        this.model = this.genAI.getGenerativeModel({ model: "gemini-2.5-flash" })
      } else {
        // 其他提供商的处理逻辑可以在这里添加
        console.warn('暂不支持该提供商，使用默认 Gemini API')
        this.genAI = new GoogleGenerativeAI(apiKey)
        this.model = this.genAI.getGenerativeModel({ model: "gemini-2.5-flash" })
      }
    }
  }

  async generateQuestions(topic: string): Promise<Question[]> {
    if (!this.model) {
      throw new Error('API Key 未设置或无效')
    }

    const prompt = `
请为主题"${topic}"生成8-12个高质量的思维训练问题。

要求：
1. 问题应该涵盖以下6个思维类别，每个类别至少1个问题：
   - critical（批判性思维）
   - creative（创意思维）
   - logical（逻辑分析）
   - system（系统思维）
   - ethical（伦理思考）
   - practical（实践应用）

2. 每个问题包含：
   - 一个引人深思的问题
   - 问题的详细描述
   - 3-4个思考提示
   - 预估思考时间
   - 难度等级（beginner/intermediate/advanced）

3. 问题应该：
   - 开放性强，没有标准答案
   - 能够激发深度思考
   - 适合不同知识背景的人
   - 具有实际意义和启发性

请严格按照以下JSON格式返回，不要包含任何其他文本：

[
  {
    "question": "问题内容",
    "description": "问题描述",
    "category": "critical|creative|logical|system|ethical|practical",
    "difficulty": "beginner|intermediate|advanced",
    "hints": ["提示1", "提示2", "提示3"],
    "estimatedTime": "10-15分钟"
  }
]
`

    try {
      let response: any
      
      if (this.config?.provider === 'deepseek' && this.config.baseUrl) {
        // DeepSeek 兼容 API 调用
        response = await this.callDeepSeekCompatibleAPI(prompt)
      } else if (this.config?.provider === 'zhipu' && this.config.baseUrl) {
        // Zhipu 兼容 API 调用
        response = await this.callZhipuCompatibleAPI(prompt)
      } else if (this.config?.provider === 'custom' && this.config.baseUrl) {
        // 自定义 API 调用
        response = await this.callCustomAPI(prompt)
      } else {
        // 默认使用 Gemini
        const result = await this.model.generateContent(prompt)
        response = await result.response
      }
      
      const text = typeof response === 'string' ? response : response.text()
      
      // 尝试解析JSON
      let questionsData
      try {
        // 清理可能的markdown代码块标记
        const cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()
        questionsData = JSON.parse(cleanText)
      } catch (parseError) {
        console.error('JSON解析失败:', parseError)
        console.log('原始响应:', text)
        throw new Error('API 返回的数据格式不正确')
      }

      // 转换为Question格式
      const questions: Question[] = questionsData.map((item: any, index: number) => ({
        id: `${this.config?.provider || 'gemini'}-${Date.now()}-${index}`,
        question: item.question,
        description: item.description,
        category: item.category as QuestionCategory,
        difficulty: item.difficulty as QuestionDifficulty,
        hints: item.hints || [],
        estimatedTime: item.estimatedTime || '15-20分钟'
      }))

      return questions
    } catch (error) {
      console.error('生成问题时出错:', error)
      if (error instanceof Error) {
        throw new Error(`生成问题失败: ${error.message}`)
      }
      throw new Error('生成问题时发生未知错误')
    }
  }

  private async callDeepSeekCompatibleAPI(prompt: string): Promise<string> {
    if (!this.config?.baseUrl || !this.config?.apiKey) {
      throw new Error('DeepSeek 兼容 API 配置不完整')
    }

    const response = await fetch(`${this.config.baseUrl}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      })
    })

    if (!response.ok) {
      throw new Error(`DeepSeek API 请求失败: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.choices[0]?.message?.content || ''
  }

  private async callZhipuCompatibleAPI(prompt: string): Promise<string> {
    if (!this.config?.baseUrl || !this.config?.apiKey) {
      throw new Error('Zhipu 兼容 API 配置不完整')
    }

    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: 'glm-4',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      })
    })

    if (!response.ok) {
      throw new Error(`Zhipu API 请求失败: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.choices[0]?.message?.content || ''
  }

  private async callCustomAPI(prompt: string): Promise<string> {
    if (!this.config?.baseUrl || !this.config?.apiKey) {
      throw new Error('自定义 API 配置不完整')
    }

    // 这里可以根据具体的自定义 API 格式进行调整
    const response = await fetch(`${this.config.baseUrl}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        prompt: prompt,
        max_tokens: 4000,
        temperature: 0.7
      })
    })

    if (!response.ok) {
      throw new Error(`自定义 API 请求失败: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.text || data.response || data.content || ''
  }
}

// 获取当前激活的模型配置
export function getActiveModelConfig(): ModelConfig | null {
  try {
    const saved = localStorage.getItem('model-settings')
    if (saved) {
      const models: ModelConfig[] = JSON.parse(saved)
      return models.find(m => m.isActive) || null
    }
  } catch (error) {
    console.error('Failed to load model config:', error)
  }
  return null
}