import { ref } from 'vue';

export const isLoading = ref(false);
export const loadingMessage = ref('');
export const currentLoadingStep = ref(0);
export const totalLoadingSteps = ref(0);

export function startLoading(message: string = '加载中...', totalSteps: number = 0) {
  isLoading.value = true;
  loadingMessage.value = message;
  currentLoadingStep.value = 0;
  totalLoadingSteps.value = totalSteps;
}

export function updateLoadingProgress(currentStep: number) {
  currentLoadingStep.value = currentStep;
}

export function stopLoading() {
  isLoading.value = false;
  loadingMessage.value = '';
  currentLoadingStep.value = 0;
  totalLoadingSteps.value = 0;
}