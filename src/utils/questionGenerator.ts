export type QuestionCategory = 'critical' | 'creative' | 'logical' | 'system' | 'ethical' | 'practical'
export type QuestionDifficulty = 'beginner' | 'intermediate' | 'advanced'

export interface Question {
  id: string
  question: string
  description: string
  category: QuestionCategory
  difficulty: QuestionDifficulty
  hints: string[]
  estimatedTime: string
}

// 问题模板
const questionTemplates = {
  critical: [
    {
      template: "如果{topic}不存在，世界会发生什么根本性变化？",
      description: "通过假设性思考，分析{topic}在当前体系中的重要性和影响。",
      hints: ["考虑直接影响", "分析连锁反应", "思考长期后果"],
      estimatedTime: "10-15分钟"
    },
    {
      template: "哪些因素可能导致{topic}的概念在未来发生根本性改变？",
      description: "分析可能影响{topic}发展和演变的关键变量。",
      hints: ["技术进步的影响", "社会观念的变化", "环境因素的作用"],
      estimatedTime: "15-20分钟"
    },
    {
      template: "关于{topic}最常见的误解是什么？为什么会产生这些误解？",
      description: "识别和分析人们对{topic}的常见认知偏差。",
      hints: ["媒体传播的影响", "个人经验的局限", "文化背景的差异"],
      estimatedTime: "12-18分钟"
    }
  ],
  creative: [
    {
      template: "如果你要重新设计{topic}，你会采用什么完全不同的方法？",
      description: "突破传统思维，想象{topic}的全新实现方式。",
      hints: ["抛弃现有假设", "从不同角度思考", "结合其他领域的经验"],
      estimatedTime: "15-25分钟"
    },
    {
      template: "如何将{topic}与看似无关的概念结合，创造出新的价值？",
      description: "探索跨领域连接，发现{topic}的创新应用可能性。",
      hints: ["寻找相似性", "转换应用场景", "重新定义功能"],
      estimatedTime: "20-30分钟"
    },
    {
      template: "如果{topic}是一个生物，它会如何进化以适应未来环境？",
      description: "用生物进化的角度思考{topic}的发展路径。",
      hints: ["环境压力分析", "适应性特征", "进化策略"],
      estimatedTime: "18-25分钟"
    }
  ],
  logical: [
    {
      template: "证明或反驳：{topic}是解决某个特定问题的最佳方案。",
      description: "运用逻辑推理，系统性地分析{topic}的有效性。",
      hints: ["定义评价标准", "比较替代方案", "分析因果关系"],
      estimatedTime: "20-30分钟"
    },
    {
      template: "如果A导致B，B导致C，那么{topic}在这个逻辑链中的位置是什么？",
      description: "分析{topic}在因果关系网络中的作用和位置。",
      hints: ["识别因果关系", "分析传导机制", "考虑反馈效应"],
      estimatedTime: "15-25分钟"
    },
    {
      template: "使用归纳法总结{topic}的核心规律，然后用演绎法预测其未来发展。",
      description: "综合运用归纳和演绎推理分析{topic}。",
      hints: ["收集相关数据", "识别模式规律", "进行逻辑推演"],
      estimatedTime: "25-35分钟"
    }
  ],
  system: [
    {
      template: "{topic}是更大系统中的哪个子系统？这个系统的其他组成部分如何与之互动？",
      description: "从系统论角度分析{topic}的定位和相互关系。",
      hints: ["识别系统边界", "分析子系统关系", "考虑系统目标"],
      estimatedTime: "20-30分钟"
    },
    {
      template: "如果{topic}发生变化，会对整个系统产生什么连锁反应？",
      description: "分析{topic}变化对系统整体的影响和反馈。",
      hints: ["追踪影响路径", "识别关键节点", "考虑系统稳定性"],
      estimatedTime: "18-28分钟"
    },
    {
      template: "从多个层次（个人、组织、社会）分析{topic}的不同表现形式。",
      description: "运用层次分析法全面理解{topic}的多维性。",
      hints: ["微观层面分析", "中观层面分析", "宏观层面分析"],
      estimatedTime: "25-35分钟"
    }
  ],
  ethical: [
    {
      template: "关于{topic}的决策中，如何平衡不同利益相关者的需求？",
      description: "分析{topic}相关决策的伦理考量和利益平衡。",
      hints: ["识别利益相关者", "分析利益冲突", "寻找平衡点"],
      estimatedTime: "20-30分钟"
    },
    {
      template: "如果{topic}的发展可能带来负面后果，我们应该如何制定防范措施？",
      description: "前瞻性思考{topic}的潜在风险和伦理责任。",
      hints: ["风险评估", "预防原则", "责任分配"],
      estimatedTime: "22-32分钟"
    },
    {
      template: "在不同文化背景下，人们对{topic}的伦理标准会有什么差异？",
      description: "探讨{topic}的跨文化伦理维度。",
      hints: ["文化价值观差异", "伦理相对主义", "普世价值观"],
      estimatedTime: "25-35分钟"
    }
  ],
  practical: [
    {
      template: "如何制定一个实施{topic}的具体行动计划？",
      description: "将{topic}转化为可执行的实际步骤。",
      hints: ["目标设定", "资源分配", "时间安排", "风险控制"],
      estimatedTime: "30-45分钟"
    },
    {
      template: "在资源有限的情况下，如何优先考虑{topic}的哪些方面？",
      description: "分析{topic}实施的优先级和资源配置策略。",
      hints: ["重要性评估", "紧急性分析", "投入产出比"],
      estimatedTime: "20-30分钟"
    },
    {
      template: "如何衡量{topic}实施的成功程度？设计具体的评价指标。",
      description: "建立{topic}效果评估的量化和质化标准。",
      hints: ["定量指标", "定性指标", "过程指标", "结果指标"],
      estimatedTime: "25-35分钟"
    }
  ]
}

export function generateQuestions(topic: string): Question[] {
  const questions: Question[] = []
  const categories = Object.keys(questionTemplates) as QuestionCategory[]
  
  // 为每个类别生成问题
  categories.forEach(category => {
    const templates = questionTemplates[category]
    const selectedTemplate = templates[Math.floor(Math.random() * templates.length)]
    
    // 随机选择难度
    const difficulties: QuestionDifficulty[] = ['beginner', 'intermediate', 'advanced']
    const difficulty = difficulties[Math.floor(Math.random() * difficulties.length)]
    
    questions.push({
      id: `${category}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      question: selectedTemplate.template.replace(/{topic}/g, topic),
      description: selectedTemplate.description.replace(/{topic}/g, topic),
      category,
      difficulty,
      hints: selectedTemplate.hints,
      estimatedTime: selectedTemplate.estimatedTime
    })
  })
  
  // 添加一些额外的随机问题
  const additionalQuestions = Math.floor(Math.random() * 4) + 2 // 2-5个额外问题
  
  for (let i = 0; i < additionalQuestions; i++) {
    const randomCategory = categories[Math.floor(Math.random() * categories.length)]
    const templates = questionTemplates[randomCategory]
    const selectedTemplate = templates[Math.floor(Math.random() * templates.length)]
    const difficulties: QuestionDifficulty[] = ['beginner', 'intermediate', 'advanced']
    const difficulty = difficulties[Math.floor(Math.random() * difficulties.length)]
    
    questions.push({
      id: `${randomCategory}-${Date.now()}-${i}-${Math.random().toString(36).substr(2, 9)}`,
      question: selectedTemplate.template.replace(/{topic}/g, topic),
      description: selectedTemplate.description.replace(/{topic}/g, topic),
      category: randomCategory,
      difficulty,
      hints: selectedTemplate.hints,
      estimatedTime: selectedTemplate.estimatedTime
    })
  }
  
  // 打乱顺序
  return questions.sort(() => Math.random() - 0.5)
}